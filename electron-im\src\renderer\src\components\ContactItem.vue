<!-- 联系人项组件 -->
<template>
  <div
    class="flex items-center p-3 cursor-pointer transition-colors hover:bg-gray-50"
    :class="{ 'bg-blue-50 border-r-2 border-blue-500': isActive }"
    @click="$emit('select', contact.id)"
  >
    <UserAvatar
      :name="contact.name"
      size="large"
      :is-online="contact.status === '在线'"
      :show-online-status="true"
    />

    <div class="ml-3 flex-1 min-w-0">
      <div class="flex items-center justify-between mb-1">
        <span class="font-medium text-sm text-gray-900">{{ contact.name }}</span>
        <div class="flex items-center gap-2">
          <span v-if="contact.hasLastMessage" class="text-xs text-gray-500">{{
            formatTime(contact.lastMessageTime)
          }}</span>
          <!-- 未读消息提示 -->
          <div
            v-if="contact.unreadCount && contact.unreadCount > 0"
            class="bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1"
          >
            {{ contact.unreadCount > 99 ? '99+' : contact.unreadCount }}
          </div>
        </div>
      </div>
      <div class="text-xs text-gray-500 whitespace-nowrap overflow-hidden text-ellipsis">
        {{ contact.lastMessage || '' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import UserAvatar from './UserAvatar.vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  unreadCount?: number
  user: User
  hasLastMessage?: boolean
}

interface Props {
  contact: Contact
  isActive: boolean
}

defineProps<Props>()

defineEmits<{
  select: [id: string]
}>()

// 时间格式化函数
const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 1000 * 60) {
    return '刚刚'
  } else if (diff < 1000 * 60 * 60) {
    return `${Math.floor(diff / (1000 * 60))}分钟前`
  } else if (diff < 1000 * 60 * 60 * 24) {
    return `${Math.floor(diff / (1000 * 60 * 60))}小时前`
  } else {
    return date.toLocaleDateString()
  }
}
</script>
